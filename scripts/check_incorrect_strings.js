const fs = require("fs");
const path = require("path");

function searchTextInFile(filePath, regex) {
  const fileContent = fs.readFileSync(filePath, "utf-8");
  return fileContent.match(regex);
}

const getResult = async () => {
  const results = [];

  async function searchTextInDirectory(
    directoryPath,
    regex,
    excludedDirs = [],
    fileTypes = [],
    excludedFiles = []
  ) {
    const files = fs.readdirSync(directoryPath);

    files.forEach((file) => {
      const fullPath = path.join(directoryPath, file);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        if (excludedDirs.includes(file)) {
          // console.log(`Excluded dir: ${fullPath}`);
          return;
        }
        searchTextInDirectory(
          fullPath,
          regex,
          excludedDirs,
          fileTypes,
          excludedFiles
        );
      } else if (stats.isFile()) {
        if (excludedFiles.includes(fullPath)) {
          // console.log(`Excluded file: ${fullPath}`);
          return;
        }
        const extname = path.extname(file).toLowerCase();
        if (fileTypes.length === 0 || fileTypes.includes(extname)) {
          const foundText = searchTextInFile(fullPath, regex);
          if (foundText) {
            console.log(`Text found in file: ${fullPath}: ${foundText}`);
            results.push(`${fullPath}: ${foundText}`);
          }
        }
      }
    });
  }

  const excludedDirs = [
    "node_modules",
    "build",
    "dist",
    "locales",
    "translate_node",
    "out",
  ];
  const excludedFiles = [
    "src/libs/i18n/index.ts",
    "scripts/check_incorrect_strings.js",
    "src/helpers/search.ts",
    "src/views/UnavailableCountries.vue",
  ];
  const fileExtensions = [
    ".ts",
    ".js",
    ".vue",
    ".svg",
    ".html",
    ".json",
    ".css",
    ".scss",
  ];

  await searchTextInDirectory(
    "./",
    /[а-яА-ЯёЁ]+/gi,
    excludedDirs,
    fileExtensions,
    excludedFiles
  );

  return results;
};

getResult().then((res) => {
  if (res.length) {
    console.error(
      `
##################################################################
=========== ERROR: Incorrect strings found in project! ===========
##################################################################
`
    );
    process.exit(1);
  }
});
