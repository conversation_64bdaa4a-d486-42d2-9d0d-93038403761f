<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useCreateCardSummary } from "@/components/CreateCardV2/useCreateCardSummary";
import { useUserExchangeRatesGet } from "@/composable/API/useUserExchangeRatesGet";
import Loader from "@/components/ui/Loader/Loader.vue";
import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import CardIssueAgreement from "@/components/CreateCardIssue/CardIssueAgreement.vue";
import UICurrencyInput from "@/components/ui/UICurrencyInput/UICurrencyInput.vue";
import PromoCodeInput from "@/components/PromoCodeInput/PromoCodeInput.vue";
import CreateCardSummary from "@/components/CreateCardSummary/CreateCardSummary.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UITransition from "@/components/ui/UITransition.vue";
import UIBlock from "@/components/ui/UIBlock/UIBlock.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import type { TCardTariffSlug } from "@/composable/Tariff/types";
import cards from "@/config/cards";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { useUserStore } from "@/stores/user";
import UltimaIssueSelectTariff from "@/components/CreateCardUltimaIssue/UltimaIssueSelectTariff.vue";
import UltimaIssuePaymentSystem from "@/components/CreateCardUltimaIssue/UltimaIssuePaymentSystem.vue";
import { sortAccountsByBalance } from "@/helpers/sortAccountsByBalance";
import {
  useAccountGet,
  useUserTariff,
  useVerificationState,
} from "@/composable";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import { useDictionaryExchangeRatesGet } from "@/composable/API/useDictionaryExchangeRatesGet";
import { useCountrySets } from "@/composable/useCountrySets";
import { useKycLimitDialog } from "@/composable/useKycLimitDialog";
import type { TBinResource } from "@/types/api/TBinResource";
import { useCardBinsGet } from "@/composable/API/useCardBinsGet";
import {
  addSecureSuffix,
  removeSecureSuffix,
  type TPaymentSystem,
  type TPaymentSystemOption,
} from "@/components/CreateCardUltimaIssue/helpers";
import { getCreateCardSummaryIntervalTitle } from "@/helpers/getCreateCardSummaryIntervalTitle";
import { RouteName } from "@/constants/route_name";
import { useRouter } from "vue-router";
import type { TTariffResource } from "@/types/api/TTariffResource";

const props = defineProps<{
  cardTariffSlug: TCardTariffSlug;
  isAutoBuy: boolean;
}>();

const emit = defineEmits<{
  issueCard: [cardForIssue: TCardForIssue];
  issueCardWithAutoBuy: [cardForIssue: TCardForIssue];
  selectCardTariff: [v: TCardTariffSlug];
  setPromoCode: [promoCodeData: TPromoCodeResource | null];
}>();

const { t } = useI18n();
const userStore = useUserStore();
const router = useRouter();
const { data: accountsData, isFetching: isFetchingAccounts } = useAccountGet();
const { userTariffData } = useUserTariff();
const accounts = computed(() => accountsData.value?.data ?? []);
const usdtAccount = computed<TUserAccountResource | undefined>(() => {
  return accounts.value?.find((account) => account.currency_id === 15);
});

const { data: ratesData, isFetching: isFetchingExchangeRates } =
  useUserExchangeRatesGet();

const { data: dropdownDisplayRatesData, isFetching: isFetchingDropdownRates } =
  useDictionaryExchangeRatesGet();

const { isActive: needVerificationAction } = useCountrySets();

// cached responses
const bins = ref<Partial<Record<TCardTariffSlug, TBinResource[]>>>({
  "ultima-weekly": [],
  "ultima-weekly-3ds": [],
  ultima: [],
  "ultima-3ds": [],
  "ultima-annually": [],
  "ultima-annually-3ds": [],
});
const binsLoaded = ref<boolean>(false);
const isSecure = ref<boolean>(true);

enum PaymentSystems {
  Visa = "visa",
  Mastercard = "mastercard",
}

const haveSecureVisaBins = computed<boolean>(() => {
  return !!bins.value[addSecureSuffix(cardForIssue.value.type)]?.some(
    (binResource) =>
      getBinPaymentSystem(binResource.bin) === PaymentSystems.Visa
  );
});

const haveInsecureVisaBins = computed<boolean>(() => {
  return !!bins.value[removeSecureSuffix(cardForIssue.value.type)]?.some(
    (binResource) =>
      getBinPaymentSystem(binResource.bin) === PaymentSystems.Visa
  );
});

const haveSecureMastercardBins = computed<boolean>(() => {
  return !!bins.value[addSecureSuffix(cardForIssue.value.type)]?.some(
    (binResource) =>
      getBinPaymentSystem(binResource.bin) === PaymentSystems.Mastercard
  );
});

const haveInsecureMastercardBins = computed<boolean>(() => {
  return !!bins.value[removeSecureSuffix(cardForIssue.value.type)]?.some(
    (binResource) =>
      getBinPaymentSystem(binResource.bin) === PaymentSystems.Mastercard
  );
});

const paymentSystemsOptions = computed<TPaymentSystemOption[]>(() => {
  const options = [];
  if (haveSecureVisaBins.value) {
    options.push({
      label: t("cards.section-payment-system-label-visa"),
      value: "visa-3ds",
      icon: "system-type-visa-v3-grey",
    });
  }
  if (haveInsecureVisaBins.value) {
    options.push({
      label: t("cards.section-payment-system-label-insecure-visa"),
      value: "visa",
      icon: "system-type-visa-v3-grey",
    });
  }
  if (haveSecureMastercardBins.value) {
    options.push({
      label: t("cards.section-payment-system-label-mastercard"),
      value: "mastercard-3ds",
      icon: "system-type-mastercard-v3-grey",
    });
  }
  if (haveInsecureMastercardBins.value) {
    options.push({
      label: t("cards.section-payment-system-label-insecure-mastercard"),
      value: "mastercard",
      icon: "system-type-mastercard-v3-grey",
    });
  }

  return options;
});

const userIsWarn = computed<boolean>(() => {
  return !!userStore.user?.show_warn;
});

const minStartBalance = computed(() => {
  return userIsWarn.value ? cards.minCardBalanceUserIsWarn : 1;
});

const defaultStartBalance = computed(() => {
  return userIsWarn.value
    ? cards.minCardBalanceUserIsWarn
    : cards.minCardBalance;
});

const errorToIssue = ref<string>("");

const cardForIssue = ref<TCardForIssue>({
  bin: "",
  type: props.cardTariffSlug,
  startBalance: defaultStartBalance.value,
  minValue: minStartBalance.value,
  accountId: undefined,
  description: "",
  count: 1,
  system: 5,
});

const promoCodeData = ref<TPromoCodeResource | null>(null);
const promoCodeWidget = ref<InstanceType<typeof PromoCodeInput> | null>(null);

// only for adv or ultima monthly tariffs
const showPromoCodeWidget = computed(() => {
  const isUltimaMonthlyTariff =
    props.cardTariffSlug.includes("ultima") &&
    ["ultima-3ds", "ultima"].includes(props.cardTariffSlug);

  return isUltimaMonthlyTariff && cardForIssue.value.count === 1;
});

const agreement = ref({
  checked: false,
  valid: true,
});

const selectedAccount = computed<TUserAccountResource | undefined>(() => {
  return accounts.value?.find(
    (account) => account.id === cardForIssue.value.accountId
  );
});

const exchangeRates = computed(() => {
  return ratesData.value?.data ?? null;
});

const dropdownDisplayRates = computed(() => {
  return dropdownDisplayRatesData.value?.data ?? null;
});

const userTariffs = computed<TTariffResource[]>(() => {
  return userTariffData.data?.value?.data ?? [];
});

const {
  isFetching: isFetchingSummary,
  userCardTariff,
  // payment
  tariffPaymentWithDiscount,
  tariffPaymentCrossed,
  promoCodeDiscountPercent,
  // starting balance
  startingBalance,
  startingBalanceCrossed,
  promoCodeBonusAmount,
  // top-up fee
  feeTopUpPercent,
  // total
  totalAmount,
  bonusAmount,
} = useCreateCardSummary(cardForIssue, selectedAccount, promoCodeData);

const { validateKycLimit, openKycLimitDialog, isValidating } =
  useKycLimitDialog();

const accountBalance = computed<number>(() => {
  return parseFloat(selectedAccount.value?.balance || "0");
});

const selectedAccountIsoCode = computed<IsoCodeNames>(() => {
  return selectedAccount.value
    ? getAccountCurrencyByCurrencyId(selectedAccount.value.currency_id).isoCode
    : IsoCodeNames.USD;
});

const setBinsData = async (slug: TCardTariffSlug) => {
  const binsData = await useCardBinsGet(slug);
  bins.value[slug] = binsData.data.value?.data ?? [];
};

const errorNotEnoughMoney = computed(() => {
  return accountBalance.value < totalAmount.value
    ? t("cards.create.error.not_enough_money")
    : "";
});

const selectedPaymentSystem = computed<TPaymentSystem>(() => {
  if (cardForIssue.value.system === 4 && isSecure.value) {
    return "visa-3ds";
  } else if (cardForIssue.value.system === 4 && !isSecure.value) {
    return "visa";
  } else if (cardForIssue.value.system === 5 && isSecure.value) {
    return "mastercard-3ds";
  } else if (cardForIssue.value.system === 5 && !isSecure.value) {
    return "mastercard";
  } else {
    return "visa";
  }
});

const summaryIntervalTitle = computed(() => {
  return t(getCreateCardSummaryIntervalTitle(props.cardTariffSlug));
});

const setCardTariffHandle = (slug: TCardTariffSlug) => {
  cardForIssue.value.type = slug;

  if (!["ultima", "ultima-3ds"].includes(slug)) {
    resetPromoCode();
  }

  let currentPaymentSystem =
    cardForIssue.value.system === 4 ? "visa" : "mastercard";

  if (isSecure.value) {
    currentPaymentSystem = currentPaymentSystem + "-3ds";
  }

  const isCurrentSystemAvailable = paymentSystemsOptions.value.some(
    (item) => item.value === currentPaymentSystem
  );

  if (!isCurrentSystemAvailable) {
    const newSystem = paymentSystemsOptions.value[0].value;
    setPaymentSystemHandle(newSystem as TPaymentSystem);
  }

  emit("selectCardTariff", slug);
};

const setDefaultCardTariff = () => {
  const defaultCardTariffSlug: TCardTariffSlug = isSecure.value
    ? "ultima-3ds"
    : "ultima";
  setCardTariffHandle(defaultCardTariffSlug);
};

const setPaymentSystemHandle = (system: TPaymentSystem) => {
  cardForIssue.value.system = system.includes("visa") ? 4 : 5;

  isSecure.value = system.includes("3ds");

  const newCardTariff = isSecure.value
    ? addSecureSuffix(cardForIssue.value.type)
    : removeSecureSuffix(cardForIssue.value.type);

  if (cardForIssue.value.type !== newCardTariff) {
    setCardTariffHandle(newCardTariff);
  }
};

const setAccountHandle = (account: TUserAccountResource): void => {
  cardForIssue.value.accountId = account.id;
  errorToIssue.value = "";
};

const setCardBalanceHandle = (value: number | null): void => {
  cardForIssue.value.startBalance = value;
  errorToIssue.value = "";
};

const setPromoCodeHandler = (data?: TPromoCodeResource) => {
  promoCodeData.value = data ?? null;
  emit("setPromoCode", promoCodeData.value);
};

const resetPromoCode = () => {
  promoCodeWidget.value?.reset();
  setPromoCodeHandler();
};

const validate = (): boolean => {
  if (accountBalance.value < totalAmount.value) {
    errorToIssue.value = t("cards.create.error.not_enough_money");
    return false;
  }

  if (
    !cardForIssue.value.startBalance ||
    cardForIssue.value.startBalance < cardForIssue.value.minValue
  ) {
    errorToIssue.value = t(
      "cards.create.error.desired_balance_have_to_be_least",
      { s: `${cardForIssue.value.minValue} $` }
    );
    return false;
  }

  if (agreement.value.checked === false) {
    agreement.value.valid = false;
    return false;
  }

  return true;
};

const validateAutoBuy = (): boolean => {
  if (
    !cardForIssue.value.startBalance ||
    cardForIssue.value.startBalance < cardForIssue.value.minValue
  ) {
    errorToIssue.value = t(
      "cards.create.error.desired_balance_have_to_be_least",
      { s: `${cardForIssue.value.minValue} $` }
    );
    return false;
  }

  if (agreement.value.checked === false) {
    agreement.value.valid = false;
    return false;
  }

  return true;
};

const { countrySetGuard } = useCountrySets();
const { isOnlyUnlimited, isVerificationAboveWelcome } = useVerificationState();
const submit = () => {
  const isValid = props.isAutoBuy ? validateAutoBuy() : validate();
  if (!isValid) return;

  if (
    needVerificationAction.value ||
    isOnlyUnlimited.value ||
    isVerificationAboveWelcome.value
  ) {
    /**
     * CreateCardMachine doesn't open verification step for Team Members
     * and will be immediately open approve card step for them
     * But Memeber with Master' actual verification mode "welcome"
     * can't create ultima card (similar behaviour as Master)
     * That's why we need open KYC dialog and return 'submit' here
     */
    if (userStore.isTeamMember && userStore.actual === "welcome") {
      openKycLimitDialog("cards");
      return;
    }

    emit("issueCard", cardForIssue.value);
    return;
  }
  countrySetGuard(
    async () => {
      const isKycLimitValid = await validateKycLimit(
        cardForIssue.value.count,
        Number(cardForIssue.value.startBalance)
      );

      if (!isKycLimitValid) {
        return;
      }

      emit("issueCard", cardForIssue.value);
    },
    async () => {
      await router.push({ name: RouteName.DASHBOARD });
    },
    () => {},
    !props.cardTariffSlug.toLowerCase().includes("ultima")
  );
};

// if usdt account positive set it, otherwise set to the largest account
const preselectAccount = () => {
  if (Number(usdtAccount.value?.balance) > 1) {
    cardForIssue.value.accountId = usdtAccount.value?.id;
  } else if (exchangeRates.value) {
    cardForIssue.value.accountId = sortAccountsByBalance(
      accounts.value,
      exchangeRates.value
    )[0]?.id;
  } else {
    cardForIssue.value.accountId = accounts.value[0].id;
  }
};

const getBinPaymentSystem = (bin: string): PaymentSystems => {
  if (bin.startsWith("5")) {
    return PaymentSystems.Mastercard;
  } else {
    return PaymentSystems.Visa;
  }
};

watch([accounts, exchangeRates], ([newAccounts, newExchangeRates]) => {
  if (newAccounts.length && newExchangeRates && !cardForIssue.value.accountId) {
    preselectAccount();
  }
});

onMounted(async () => {
  // load bins for all tariffs to cache
  const slugs: TCardTariffSlug[] = [
    "ultima-weekly",
    "ultima-weekly-3ds",
    "ultima",
    "ultima-3ds",
    "ultima-annually",
    "ultima-annually-3ds",
  ];
  binsLoaded.value = false;
  await Promise.all(slugs.map((slug) => setBinsData(slug)));
  binsLoaded.value = true;

  setDefaultCardTariff();
});
</script>

<template>
  <UITransition>
    <Loader
      v-if="
        isFetchingSummary ||
        isFetchingAccounts ||
        isFetchingExchangeRates ||
        isFetchingDropdownRates
      " />
    <div
      v-else
      class="ultima-issue-form">
      <!-- Select Ultima tariff -->
      <div class="flex flex-col w-full space-y-2.5">
        <div class="flex w-full text-4.5 leading-5 text-fg-primary font-medium">
          {{ $t("cards.section-plans-title") }}
        </div>

        <UltimaIssueSelectTariff
          :selected-tariff="userCardTariff"
          :user-tariffs="userTariffs"
          :is-secure="isSecure"
          @select-card-tariff="setCardTariffHandle" />
      </div>
      <!-- Select Payment System -->
      <UltimaIssuePaymentSystem
        v-if="binsLoaded"
        :payment-system="selectedPaymentSystem"
        :payment-systems-options="paymentSystemsOptions"
        @select-system="setPaymentSystemHandle" />

      <!-- Select balance -->
      <div class="flex flex-col w-full">
        <UICurrencyInput
          data-testid="start-balance-input"
          :currency="IsoCodeNames.USD"
          :label="$t('Desired card balance')"
          :min="minStartBalance"
          :model-value="cardForIssue.startBalance"
          class="w-full"
          size="m"
          @update:model-value="setCardBalanceHandle" />

        <div class="grid grid-cols-4 gap-2 mt-2">
          <UIButton
            v-for="item of [50, 100, 250, 500]"
            :key="item"
            color="grey-solid"
            size="s"
            data-testid="start-balance-button"
            :disabled="
              userStore.user.show_warn
                ? item < cards.minCardBalanceUserIsWarn
                : false
            "
            @click="setCardBalanceHandle(item)">
            <span class="text-4 leading-8 font-medium"> {{ item }} $ </span>
          </UIButton>
        </div>
      </div>

      <!-- Select Account -->
      <AccountsAndCardsSelect
        v-if="accounts && dropdownDisplayRates && !isAutoBuy"
        data-testid="account-select"
        :accounts="accounts as TUserAccountResource[]"
        :error="errorNotEnoughMoney"
        :label="$t('label.paymentMethod')"
        :model-value="cardForIssue.accountId"
        :rates="dropdownDisplayRates"
        :tariffs="[]"
        class="w-full"
        searchable
        size="l"
        @change="(account) => setAccountHandle(account as TUserAccountResource)" />

      <!-- Total + PromoCode -->
      <div class="w-full flex flex-col space-y-2">
        <CreateCardSummary
          :interval-title="summaryIntervalTitle"
          :account-iso-code="selectedAccountIsoCode"
          :payment="tariffPaymentWithDiscount"
          :payment-crossed="tariffPaymentCrossed"
          :payment-discount-percent="promoCodeDiscountPercent"
          :security-additional-cost="0"
          :starting-balance="startingBalance"
          :starting-balance-bonus="promoCodeBonusAmount"
          :starting-balance-crossed="startingBalanceCrossed"
          :top-up-fee="feeTopUpPercent"
          :is-adv-with-sub-active="false"
          :bonus-amount="bonusAmount"
          :total="totalAmount" />

        <div
          v-if="showPromoCodeWidget"
          class="w-full">
          <PromoCodeInput
            ref="promoCodeWidget"
            mode="card"
            @set-promo-code="
              (promoCadeData) => setPromoCodeHandler(promoCadeData)
            " />
        </div>
      </div>

      <!-- Attention -->
      <UIBlock
        :background="'red'"
        class="w-full">
        <template #title>
          {{ $t("label.attention") }}
        </template>
        <template #content>
          {{
            $t("warning.notCompatibleWithWallets", {
              w: "Apple Pay, Google Pay",
            })
          }}
        </template>
      </UIBlock>

      <!-- Agreement -->
      <CardIssueAgreement v-model="agreement" />

      <!-- Submit -->
      <div class="flex flex-col items-center w-full space-y-4">
        <span
          v-if="errorToIssue"
          class="text-sm text-error-light"
          data-cy="errorToIssue">
          {{ errorToIssue }}
        </span>
        <UIButton
          :is-loading="isValidating"
          class="w-full"
          color="black"
          data-cy="order_button"
          @click="submit">
          {{ $t("continue") }}
        </UIButton>
      </div>
    </div>
  </UITransition>
</template>

<style lang="scss" scoped>
.ultima-issue-form {
  @apply flex flex-col items-center space-y-10 max-w-[29.5rem] px-4 mx-auto;
}
</style>
