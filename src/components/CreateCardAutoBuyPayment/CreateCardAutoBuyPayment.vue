<script lang="ts" setup>
import { computed, onUnmounted, reactive, ref, toRef, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useCreateCardSummary } from "@/components/CreateCardV2/useCreateCardSummary";
import { useCallToast } from "@/composable/useCallToast";
import { useClipboard } from "@vueuse/core";
import { Skeletor } from "vue-skeletor";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";
import UITransition from "@/components/ui/UITransition.vue";
import UIBlock from "@/components/ui/UIBlock/UIBlock.vue";
import CreateCardSummary from "@/components/CreateCardSummary/CreateCardSummary.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import { IsoCodeNames } from "@/constants/iso_code_names";
import QrCode from "qrcode";
import { useUserStore } from "@/stores/user";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import {
  checkAndCreateAutoBuy,
  checkAutoBuyTimer,
  checkAutoBuyTransaction,
  type IAutoBuyStatus,
} from "@/composable/CardAutoBuy";
import type {
  IAutoBayStoreDto,
  TAutoBuyStatusCode,
} from "@modules/services/autoBuy";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import {
  useAccountGet,
  useSubscriptionPlusCardAutobuy1Experiment,
  useTracker,
} from "@/composable";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import type { TValueOf } from "@/types/TValueOf";
import { isNil } from "lodash";

const props = defineProps<{
  cardForIssue: TCardForIssue;
  promoCodeData: TPromoCodeResource | null;
}>();

const emit = defineEmits(["autoBuySuccess"]);

const { t } = useI18n();
const { copy } = useClipboard();
const userStore = useUserStore();
const { subscriptionsStatus } = useSubscriptionsInfo();
const { data: accountsData, isFetching: isFetchingAccounts } = useAccountGet();
const { getValue } = useSubscriptionPlusCardAutobuy1Experiment();
const { isActive: isAdvWithSubActive } = getValue();
const tracker = useTracker();

const accounts = computed<TUserAccountResource[]>(() => {
  return accountsData.value?.data ?? [];
});

const qrCodeURL = ref("");
const selectedAccountAddress = ref("");
const transactionCheckVal = ref<string>("");
const autoBuyId = ref<number | null>(null);
const currentAutoBuyStatus = reactive<IAutoBuyStatus>({
  status: 10,
  title: "Pending",
});

const autoBuyCheckTimer = ref<ReturnType<typeof setInterval> | null>(null);
const transactionCheckTimer = ref<any>(null);
const selectedNetworkIsoCode = ref<NetworkIsoCode>(IsoCodeNames.USDT);

const selectedNetworkAccount = computed<TUserAccountResource>(() => {
  return (
    accounts.value.find((account) => {
      return (
        getAccountCurrencyByCurrencyId(account.currency_id).isoCode ===
        selectedNetworkIsoCode.value
      );
    }) || accounts.value[0]
  );
});

const {
  isFetching: isFetchingSummary,
  // payment
  tariffPaymentWithDiscount,
  tariffPaymentCrossed,
  promoCodeDiscountPercent,
  // starting balance
  startingBalance,
  startingBalanceCrossed,
  promoCodeBonusAmount,
  // top-up fee
  feeTopUpPercent,
  // total
  totalAmount,
  bonusAmount,
  extraSmallTariffPrice,
} = useCreateCardSummary(
  toRef(props, "cardForIssue"),
  selectedNetworkAccount,
  toRef(props, "promoCodeData")
);

type NetworkIsoCode = Extract<IsoCodeNames, "BTC" | "USDT">;

const networkByIsoCodes: Record<NetworkIsoCode, string> = {
  USDT: "Tether USDT (TRC20)",
  BTC: "BTC",
};

type TNetworkLabel = TValueOf<typeof networkByIsoCodes>;
type TNetworkOption = {
  label: TNetworkLabel;
  value: NetworkIsoCode;
};

const accountSelectOptions: TNetworkOption[] = [
  {
    label: networkByIsoCodes["USDT"],
    value: IsoCodeNames.USDT,
  },
  {
    label: networkByIsoCodes["BTC"],
    value: IsoCodeNames.BTC,
  },
];

const selectNetwork = (
  network: Array<string | number> | string | number | null
) => {
  selectedNetworkIsoCode.value = network as NetworkIsoCode;

  tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_AUTOBUY, {
    crypto: selectedNetworkIsoCode.value,
  });
};

const showSubscriptionTariffPrice = computed<boolean>(() => {
  return !subscriptionsStatus.value && !!isAdvWithSubActive.value;
});

const promoCode = computed<string>(() => {
  return props.promoCodeData?.code ?? "";
});

const autoBuyStoreConfig = reactive<IAutoBayStoreDto>({
  type: props.cardForIssue.type,
  start_balance: String(props.cardForIssue.startBalance ?? 0),
  code: promoCode.value,
  system: Number(props.cardForIssue.system),
});

const autoBuy = async () => {
  const autoBuy = await checkAndCreateAutoBuy(autoBuyStoreConfig);

  if (!autoBuy?.status) {
    currentAutoBuyStatus.title = "Error";
    return;
  }

  if (promoCode.value) {
    await tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
      code: promoCode.value,
    });

    await tracker.setUserProperty(TrackerEvent.PROMOCODE_USED, promoCode.value);
  }

  autoBuyId.value = autoBuy.data?.id ?? null;

  autoBuyCheckTimer.value = checkAutoBuyTimer(
    autoBuy.data?.id!,
    (status: TAutoBuyStatusCode) => {
      switch (status) {
        case 20:
          clearInterval(autoBuyCheckTimer.value!);
          currentAutoBuyStatus.title = "Payments gone";
          emit("autoBuySuccess");
          break;
        case 30:
          clearInterval(autoBuyCheckTimer.value!);
          break;
      }
    }
  );

  transactionCheckTimer.value = setInterval(async () => {
    const res = await checkAutoBuyTransaction(selectedAccountAddress.value);
    if (!res.status) {
      clearInterval(transactionCheckTimer.value);
      return;
    }
    transactionCheckVal.value = res.data;
    if (transactionCheckVal.value !== res.data) {
      clearInterval(transactionCheckTimer.value);
      currentAutoBuyStatus.title = "In progress";
    }
  }, 3000);

  tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_SHOW_QR);
};

autoBuy();

const generateQrCodeURL = async (address: string) => {
  if (!address) return;
  qrCodeURL.value = await QrCode.toDataURL(address);
};

const copyValue = (value: string) => {
  copy(value);
  useCallToast({
    title: t("cards.copied"),
  });
};

watch(
  () => selectedNetworkAccount,
  (newValue) => {
    if (!newValue) return;
    selectedAccountAddress.value = newValue.value?.addresses[0]?.["address"];
    generateQrCodeURL(selectedAccountAddress.value);
  },
  { deep: true }
);

userStore.getUserFees();

const clearAllTimers = () => {
  if (autoBuyCheckTimer.value) {
    clearInterval(autoBuyCheckTimer.value);
  }
  if (transactionCheckTimer.value) {
    clearInterval(transactionCheckTimer.value);
  }
};

onUnmounted(() => {
  clearAllTimers();
});
</script>

<template>
  <UITransition>
    <Loader v-if="isFetchingSummary || isFetchingAccounts" />
    <div
      v-else
      class="card-auto-buy-payment">
      <div class="w-full">
        <UISelect
          :cleared="false"
          :label="$t('cards.section-payment-method-label')"
          :model-value="selectedNetworkIsoCode"
          :multiple="false"
          :options="accountSelectOptions"
          size="l"
          @update:model-value="selectNetwork" />
      </div>
      <!-- Total -->
      <CreateCardSummary
        :account-iso-code="selectedNetworkIsoCode"
        :payment="tariffPaymentWithDiscount"
        :payment-crossed="tariffPaymentCrossed"
        :payment-discount-percent="promoCodeDiscountPercent"
        :starting-balance="startingBalance"
        :starting-balance-bonus="promoCodeBonusAmount"
        :starting-balance-crossed="startingBalanceCrossed"
        :top-up-fee="feeTopUpPercent"
        :is-adv-with-sub-active="
          showSubscriptionTariffPrice && !cardForIssue.type.includes('ultima')
        "
        :subscription-tariff-price="extraSmallTariffPrice"
        :bonus-amount="bonusAmount"
        :total="totalAmount" />

      <!-- Payment credentials -->
      <UITransition>
        <Skeletor
          v-if="!qrCodeURL"
          as="div"
          class="rounded"
          height="310"
          width="100%" />
        <div
          v-else
          class="flex flex-col space-y-2">
          <UIBlock>
            <template #title>
              {{ $t("cards.transfer-funds") }}
            </template>
            <template #content>
              <div class="flex flex-row space-x-4">
                <div class="flex flex-none">
                  <img
                    :src="qrCodeURL"
                    alt="Address"
                    class="rounded w-[7.25rem] h-[7.25rem]" />
                </div>
                <div class="flex flex-auto flex-col justify-between">
                  <div class="flex flex-none flex-col">
                    <div
                      class="flex flex-none text-fg-secondary text-4 leading-5">
                      {{ $t("cards.transfer-network") }}
                    </div>
                    <div
                      class="flex flex-none text-fg-primary text-4 leading-5">
                      {{ networkByIsoCodes[selectedNetworkIsoCode] }}
                    </div>
                  </div>
                  <div class="flex flex-none flex-col">
                    <div class="text-fg-secondary text-4 leading-5">
                      {{ $t("cards.wallet-address") }}
                    </div>
                    <div
                      class="flex flex-none flex-row text-fg-primary text-4 leading-5 break-all">
                      <div
                        class="flex flex-auto"
                        data-testid="selected-account-address">
                        {{ selectedAccountAddress }}
                      </div>
                      <div class="flex">
                        <DynamicIcon
                          v-tooltip="{ content: $t('Copy') }"
                          class="w-7 cursor-pointer text-fg-primary"
                          name="copy"
                          @click="copyValue(selectedAccountAddress)" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </UIBlock>

          <UIBlock>
            <template #content>
              <div class="flex flex-none items-center">
                <div
                  class="flex flex-none mr-3 p-1.5 bg-bg-level-0 rounded-full">
                  <DynamicIcon
                    class="w-6 h-6 text-fg-secondary"
                    name="clock" />
                </div>
                <div class="flex flex-auto flex-col">
                  <div
                    class="flex flex-none text-fg-secondary text-4 leading-5">
                    {{ $t("cards.transaction-status") }}
                  </div>
                  <div class="flex flex-none text-4 leading-5 mt-0.5">
                    <span
                      v-if="currentAutoBuyStatus.title === 'Pending'"
                      class="text-fg-blue">
                      {{ $t("transactions-state-pending") }}
                    </span>
                    <span
                      v-if="currentAutoBuyStatus.title === 'In progress'"
                      class="text-fg-orange">
                      {{ $t("transactions-state-progress") }}
                    </span>
                    <span
                      v-if="currentAutoBuyStatus.title === 'Payments gone'"
                      class="text-fg-green">
                      {{ $t("transactions-state-finished") }}
                    </span>
                    <span
                      v-if="currentAutoBuyStatus.title === 'Error'"
                      class="text-red-600">
                      Error
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </UIBlock>

          <div
            v-if="selectedNetworkIsoCode === IsoCodeNames.USDT"
            class="flex text-center text-fg-secondary text-3.5 leading-4">
            {{ $t("cards.auto-buy-usdt-attention") }}
          </div>
        </div>
      </UITransition>

      <!-- Attention -->
      <UIBlock>
        <template #title>
          {{ $t("createCard.payments.attention") }}
        </template>
        <template #content>
          <ul
            class="flex flex-col gap-2 list-disc pl-5 text-4 text-fg-primary mt-3">
            <li class="leading-5">
              {{ $t("createCard.payments.attention.anotherNetwork") }}
            </li>
            <li
              v-if="
                isNil(userStore.userFees.deposit_fee_usdt) ||
                Number(userStore.userFees.deposit_fee_usdt) > 0
              "
              class="leading-5">
              {{ $t("createCard.payments.network.depositServiceFee") }}
            </li>
            <li
              v-if="selectedNetworkIsoCode === IsoCodeNames.BTC"
              class="leading-5">
              {{ $t("attention-deposit-with-btc-auto-buy") }}
            </li>
          </ul>
        </template>
      </UIBlock>
    </div>
  </UITransition>
</template>

<style lang="scss" scoped>
.card-auto-buy-payment {
  @apply flex flex-col items-center space-y-10 max-w-[29.5rem] px-4 mx-auto;
}
</style>
