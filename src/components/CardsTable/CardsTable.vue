<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UiTransition from "@/components/ui/UITransition.vue";
import { Skeletor } from "vue-skeletor";
import { computed, ref, triggerRef } from "vue";
import { TABLE_GRID_VIEW_NAMES } from "@/constants/table_grid_view_names";
import UITableOrGridViewChecker from "@/components/ui/UITableOrGridViewChecker.vue";
import CardsTableUserTableView from "@/components/CardsTable/CardsTableUserTableView.vue";
import CardsTableUserGridView from "@/components/CardsTable/CardsTableUserGridView.vue";
import {
  type CardsReq,
  useCardsTableState,
} from "@/components/CardsTable/useCardsTableState";
import CardsTableMaster from "@/components/CardsTable/CardsTableMaster.vue";
import CardsTableFilters from "@/components/CardsTable/CardsTableFilters.vue";
import SubscriptionCardsStatuses from "@/components/Widgets/SubscriptionCardsStatuses/SubscriptionCardsStatuses.vue";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useCardsTableViewChecker } from "./useCardsTableViewChecker";
import { useCardsGet } from "@/composable/API/useCardsGet";
import { useUserStore } from "@/stores/user";
import { RouteName } from "@/constants/route_name";
import { useCardIdPatch } from "@/composable/API/useCardIdPatch";
import { useBusinessMembersCardsGet } from "@/composable/API/useBusinessMembersCardsGet";
import { type LocationQueryRaw, useRoute, useRouter } from "vue-router";
import type { IUITableSortModel } from "@/components/ui/UITableV2/types";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import {
  TOAST_TYPE,
  useCallToast,
  useUserSpecialCondition,
} from "@/composable";
import CardsTableNoCardsWithSpecial from "@/components/CardsTable/CardsTableNoCardsWithSpecial.vue";
import CardsTableNoCards from "@/components/CardsTable/CardsTableNoCards.vue";
import { useEventHooks } from "@/composable/useEventHooks";
import { useI18n } from "vue-i18n";
import { useDebounceFn } from "@vueuse/core";
import {
  adapt,
  normalize,
  removeNewProperties,
} from "@/components/CardsTable/cardsTableUtils";
import { useCardIssueRedirect } from "@/composable/useCardIssueRedirect";

const [subscriptionBlock] = useAutoAnimate({ duration: 100 });

const {
  subscriptionsStatus,
  isFetching: isSubscriptionsInfoLoading,
  subscriptionsInfo,
  cardsToRelease,
} = useSubscriptionsInfo();
const { redirectToCardIssue } = useCardIssueRedirect(
  subscriptionsStatus,
  cardsToRelease
);
const userStore = useUserStore();
const { t } = useI18n();

userStore.getUser();

const router = useRouter();
const route = useRoute();

const { hasSpecialCondition, isFetching: isSpecialConditionLoading } =
  useUserSpecialCondition();

const isSubscriptionsDataLoading = computed<boolean>(() => {
  return (
    (isSubscriptionsInfoLoading.value ||
      isCardsDataLoading.value ||
      isSpecialConditionLoading.value) &&
    !hiddenLoading.value
  );
});

const clearReqConfig = computed(() => {
  return Object.keys(reqConfig.value).reduce(
    (acc: Record<string, unknown>, item: string) => {
      if (reqConfig.value[item]) {
        acc[item] = reqConfig.value[item];
      } else {
        acc[item] = undefined;
      }
      return acc;
    },
    {}
  );
});

/**
 * Replacing the type parameter with card_type to avoid conflict
 * with the open transaction window and skip user_id param for member table
 */
const replaceQueryReqConfig = computed(() => {
  return Object.keys(clearReqConfig.value).reduce(
    (acc: Record<string, unknown>, item: string) => {
      if (isMemberTable.value && item === "user_id") {
        return acc;
      } else if (item === "type") {
        acc["card_type"] = clearReqConfig?.value[item];
      } else {
        acc[item] = clearReqConfig?.value[item];
      }
      return acc;
    },
    {}
  );
});

const apiRequestFilters = computed(() => {
  return adapt(clearReqConfig.value);
});

const routerFilters = computed(() => {
  return {
    ...replaceQueryReqConfig.value,
  } as LocationQueryRaw;
});

const updateRouteQuery = () => {
  router.replace({
    query: {
      ...routerFilters.value,
    },
  });
};

const getTagsFromQuery = (): string[] | [] => {
  if (route?.query?.["tags-slugs"]) {
    if (Array.isArray(route?.query?.["tags-slugs"])) {
      return route?.query?.["tags-slugs"] as string[];
    } else {
      return [route?.query?.["tags-slugs"]];
    }
  } else return [];
};

const { isTeamTable, isMemberTable, memberId } = useCardsTableState();

const reqConfig = ref<Record<string, unknown>>({
  "tags-slugs": getTagsFromQuery(),
  search: route?.query?.search ?? "",
  simple_status:
    Object.keys(route.query).length === 0
      ? "active"
      : route?.query?.simple_status,
  type: route?.query?.card_type ?? "",
  user_id: isMemberTable.value ? memberId.value : route?.query?.user_id ?? null,
  per_page: route?.query?.per_page ? Number(route?.query?.per_page) : 24,
  direction: route.query.direction ?? null,
  sort: route?.query?.sort ?? "created_at",
});

const initializeCardsFilter = () => {
  const urlParams = route.query;

  const conf: Record<string, unknown> = {
    ...urlParams,
    ...reqConfig.value,
  };

  if (conf.card_type) {
    delete conf.card_type;
  }

  const normalizedConfig = normalize(conf as CardsReq);

  Object.assign(reqConfig.value, normalizedConfig);
  updateRouteQuery();
};

initializeCardsFilter();

const shouldShowNoCardsView = computed(() => {
  return userStore?.summary?.cards_count === 0 && !isTeamTable.value;
});

const setCardsReqConfig = (config: CardsReq) => {
  const updatedFilters = removeNewProperties({ ...config });
  const normalizedFilters = normalize(updatedFilters);
  reqConfig.value = { ...normalizedFilters };
  updateRouteQuery();
};

const cardsData = isTeamTable.value
  ? useBusinessMembersCardsGet(apiRequestFilters, {}, 0)
  : useCardsGet(apiRequestFilters, {}, 0);

const {
  data: cards,
  isFetching: isCardsDataLoading,
  execute: executeCardsData,
} = cardsData;

const hiddenLoading = ref<boolean>(false);

const updateCards = async () => {
  await executeCardsData();
};

const { changeCardBalanceHook } = useEventHooks();

changeCardBalanceHook.on(async () => {
  hiddenLoading.value = true;
  console.log("DEBUG ON ");

  await updateCards();
  hiddenLoading.value = false;
});

const setUserPageHandle = (page: number) => {
  setCardsReqConfig({
    ...reqConfig.value,
    page,
  });
};

const setPerPageHandle = (v: number) => {
  setCardsReqConfig({
    ...reqConfig.value,
    per_page: v,
  });
};

const setSortHandle = (sort: IUITableSortModel) => {
  setCardsReqConfig(sort as CardsReq);
};

const { currentTableView } = useCardsTableViewChecker();

const setFiltersHandler = (value: CardsReq) => {
  setCardsReqConfig(value);
};

const setFavorite = useOptimisticFavoriteCardHandler(cards);

const clearFiltersHandle = () => {
  const user_id = isMemberTable.value ? memberId.value : "";
  setCardsReqConfig({
    search: "",
    type: "",
    simple_status: null,
    "tags-slugs": [],
    page: 1,
    per_page: 24,
    user_id,
    sort: "created_at",
  });
};

const isShowResetFilterButton = computed(() => {
  return Boolean(
    reqConfig.value.type ||
      reqConfig.value.simple_status ||
      (reqConfig.value.user_id && !isMemberTable.value) ||
      (reqConfig.value["tags-slugs"] as number[])?.length ||
      reqConfig.value.search
  );
});

function useOptimisticFavoriteCardHandler(cardsRef: typeof cards) {
  const initialStateMap = new Map<number, boolean>();
  const debounceMap = new Map<number, typeof toggleFavoriteRequest>();
  const ongoingRequestsMap = new Map<
    number,
    ReturnType<typeof useCardIdPatch>
  >();

  const DEBOUNCE_DELAY = 300;

  const getDebouncedRequestById = (
    id: number
  ): typeof toggleFavoriteRequest => {
    const fn = debounceMap.get(id);

    if (fn) {
      return fn;
    }

    const newFn = useDebounceFn(toggleFavoriteRequest, DEBOUNCE_DELAY);

    debounceMap.set(id, newFn);

    return newFn;
  };

  const toggleFavoriteRequest = (state: boolean, id: number) => {
    let ongoingRequest = ongoingRequestsMap.get(id);

    if (ongoingRequest) {
      ongoingRequest.abort();
    }

    ongoingRequest = useCardIdPatch(id, { favorite: state });

    ongoingRequestsMap.set(id, ongoingRequest);

    ongoingRequest.then((response) => {
      const { aborted, error, data } = response;

      if (aborted.value) {
        return;
      }

      if (error.value || !data.value?.success) {
        const initialState = initialStateMap.get(id) as boolean;
        toggleUI(initialState, id);

        useCallToast({
          title: t("errors.universal-request-error"),
          options: {
            type: TOAST_TYPE.ERROR,
          },
        });
      }

      initialStateMap.delete(id);
      ongoingRequestsMap.delete(id);
      debounceMap.delete(id);
    });
  };

  const toggleUI = (state: boolean, id: number) => {
    if (!cardsRef.value?.data) {
      return;
    }

    cardsRef.value = {
      data: cardsRef.value.data.map((card) => {
        if (card.id === id) {
          return {
            ...card,
            favorite: Number(state),
          };
        }
        return card;
      }),
      meta: cardsRef.value.meta,
    };

    triggerRef(cardsRef);
  };

  return (state: boolean, id: number) => {
    toggleUI(state, id);

    if (!initialStateMap.has(id)) {
      initialStateMap.set(id, !state);
    }

    const debouncedRequest = getDebouncedRequestById(id);

    debouncedRequest(state, id);
  };
}

const tableTitle = computed(() => {
  return route.name === RouteName.TEAM_CARDS
    ? t("common.team.cards-table.title")
    : t("cardsTable.title");
});

const handleNewCardIssueBtnClick = () => {
  if (isSpecialConditionLoading.value) {
    return;
  }

  redirectToCardIssue();
};
</script>

<template>
  <UiTransition>
    <div v-if="shouldShowNoCardsView">
      <CardsTableNoCardsWithSpecial
        v-if="hasSpecialCondition && !subscriptionsStatus" />
      <CardsTableNoCards v-else />
    </div>
    <div v-else>
      <!--    table header -->
      <div class="flex items-center justify-between">
        <h1 class="text-5 font-medium">{{ tableTitle }}</h1>
        <!--      table view checker-->
        <div class="flex items-center gap-2">
          <UITableOrGridViewChecker
            v-if="currentTableView"
            v-model="currentTableView" />
          <UIButton
            v-if="!isTeamTable"
            class="sm:min-w-34 font-medium"
            color="black"
            size="s"
            @click="handleNewCardIssueBtnClick">
            {{ $t("btn.newCard") }}

            <template #left>
              <DynamicIcon name="card-physical" />
            </template>
          </UIButton>
        </div>
        <!--      table view checker end-->
      </div>

      <!-- subscriptions card statuses -->
      <div
        v-if="subscriptionsStatus && !isMemberTable && !userStore.isTeamMember"
        ref="subscriptionBlock"
        class="mt-4">
        <SubscriptionCardsStatuses />
      </div>

      <!--    filters -->
      <UiTransition mode="out-in">
        <div>
          <Suspense>
            <CardsTableFilters
              key="filters"
              :filters="reqConfig"
              :is-loading-cards="isSubscriptionsDataLoading"
              :is-show-reset-button="isShowResetFilterButton"
              @set-filters="setFiltersHandler"
              @clear-filters="clearFiltersHandle" />

            <template #fallback>
              <div
                key="filterSkeleton"
                class="grid grid-cols-6 gap-4 my-3.5 w-full"
                style="width: 70%">
                <Skeletor
                  v-for="(_, index) in 6"
                  :key="index"
                  as="div"
                  class="rounded"
                  height="32" />
              </div>
            </template>
          </Suspense>
        </div>
      </UiTransition>

      <!--    table -->
      <div v-if="!isTeamTable">
        <UiTransition
          mode="out-in"
          name="ui-fade">
          <CardsTableUserTableView
            v-if="currentTableView === TABLE_GRID_VIEW_NAMES.TABLE"
            :cards="cards ? cards : null"
            :is-loading="isSubscriptionsDataLoading"
            :per-page="(reqConfig.per_page as number)"
            :subscription-status="subscriptionsInfo?.status"
            @set-page="setUserPageHandle"
            @set-sort="setSortHandle"
            @rename-card="updateCards"
            @favorite-set="setFavorite"
            @clear-filters="clearFiltersHandle"
            @set-per-page="setPerPageHandle" />

          <CardsTableUserGridView
            v-if="currentTableView === TABLE_GRID_VIEW_NAMES.GRID"
            :cards="cards ? cards : null"
            :is-loading="isSubscriptionsDataLoading"
            :per-page="(reqConfig.per_page as number)"
            :subscription-status="subscriptionsInfo?.status"
            @set-page="setUserPageHandle"
            @set-per-page="setPerPageHandle"
            @clear-filters="clearFiltersHandle"
            @set-favorite="setFavorite" />
        </UiTransition>
      </div>

      <div v-else>
        <UiTransition
          mode="out-in"
          name="ui-fade">
          <CardsTableMaster
            v-if="currentTableView === TABLE_GRID_VIEW_NAMES.TABLE"
            :cards="cards ? cards : null"
            :is-loading="isSubscriptionsDataLoading"
            :per-page="(reqConfig.per_page as number)"
            :subscription-status="subscriptionsInfo?.status"
            @set-page="setUserPageHandle"
            @clear-filters="clearFiltersHandle"
            @set-sort="setSortHandle"
            @set-per-page="setPerPageHandle" />

          <CardsTableUserGridView
            v-if="currentTableView === TABLE_GRID_VIEW_NAMES.GRID"
            :cards="cards ? cards : null"
            :is-loading="isSubscriptionsDataLoading"
            :per-page="(reqConfig.per_page as number)"
            :subscription-status="subscriptionsInfo?.status"
            is-grouped
            @set-page="setUserPageHandle"
            @set-per-page="setPerPageHandle"
            @clear-filters="clearFiltersHandle"
            @set-favorite="setFavorite" />
        </UiTransition>
      </div>
    </div>
  </UiTransition>
</template>
